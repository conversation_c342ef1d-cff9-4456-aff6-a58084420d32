<?php
session_start();
require_once '../includes/db_connect.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

// Get student data
$students = [];
$sql = "SELECT s.*, c.class_name, se.session_name 
        FROM students s 
        LEFT JOIN classes c ON s.class_id = c.class_id 
        LEFT JOIN sessions se ON s.session_id = se.session_id 
        ORDER BY s.student_name";
$result = $conn->query($sql);
if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী আইডি কার্ড - জামিয়া ফরিদিয়া আরাবিয়া ওয়াকফ</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Hind Siliguri', sans-serif !important;
        }
        
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .id-card {
            width: 350px;
            height: 220px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 15px;
            margin: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            color: white;
            position: relative;
            overflow: hidden;
            display: inline-block;
        }
        
        .id-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            z-index: 1;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .school-name {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 2px;
        }
        
        .card-title {
            font-size: 12px;
            font-weight: 600;
            background: rgba(255,255,255,0.2);
            padding: 3px 8px;
            border-radius: 10px;
            display: inline-block;
        }
        
        .card-body {
            display: flex;
            gap: 15px;
            position: relative;
            z-index: 2;
        }
        
        .student-photo {
            width: 70px;
            height: 80px;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .student-info {
            flex: 1;
        }
        
        .info-row {
            margin-bottom: 6px;
            font-size: 11px;
        }
        
        .info-label {
            font-weight: 600;
            display: inline-block;
            width: 60px;
        }
        
        .info-value {
            font-weight: 500;
        }
        
        .student-id {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 10px;
            font-weight: 600;
            background: rgba(255,255,255,0.2);
            padding: 2px 6px;
            border-radius: 5px;
            z-index: 2;
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .cards-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-top: 80px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .print-btn, .back-btn {
                display: none;
            }
            
            .id-card {
                margin: 5px;
                break-inside: avoid;
            }
            
            .cards-container {
                margin-top: 0;
            }
        }
        
        @media (max-width: 768px) {
            .id-card {
                width: 320px;
                height: 200px;
            }
            
            .cards-container {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="students.php" class="btn btn-secondary back-btn">
        <i class="fas fa-arrow-left me-2"></i>ফিরে যান
    </a>
    
    <!-- Print Button -->
    <button onclick="window.print()" class="btn btn-primary print-btn">
        <i class="fas fa-print me-2"></i>প্রিন্ট করুন
    </button>
    
    <div class="cards-container">
        <?php foreach($students as $student): ?>
        <div class="id-card">
            <div class="card-header">
                <div class="school-name">জামিয়া ফরিদিয়া আরাবিয়া ওয়াকফ</div>
                <div class="card-title">শিক্ষার্থী পরিচয়পত্র</div>
            </div>
            
            <div class="card-body">
                <div class="student-photo">
                    <?php if(!empty($student['photo'])): ?>
                        <img src="../uploads/students/<?php echo htmlspecialchars($student['photo']); ?>" 
                             alt="ছবি" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                    <?php else: ?>
                        <i class="fas fa-user" style="font-size: 24px; color: #999;"></i>
                    <?php endif; ?>
                </div>
                
                <div class="student-info">
                    <div class="info-row">
                        <span class="info-label">নাম:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['student_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">পিতা:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['father_name']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">ক্লাস:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">সেশন:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">মোবাইল:</span>
                        <span class="info-value"><?php echo htmlspecialchars($student['phone']); ?></span>
                    </div>
                </div>
            </div>
            
            <div class="student-id">
                ID: <?php echo str_pad($student['student_id'], 4, '0', STR_PAD_LEFT); ?>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
