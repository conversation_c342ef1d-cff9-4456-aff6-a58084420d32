<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if department_id column exists in subjects table, if not add it
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE subjects ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Create subject_categories table if it doesn't exist
$categoriesTableQuery = "CREATE TABLE IF NOT EXISTS subject_categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    department_id INT(11) NULL,
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";
$conn->query($categoriesTableQuery);

// Create subject_category_mapping table for many-to-many relationship
$mappingTableQuery = "CREATE TABLE IF NOT EXISTS subject_category_mapping (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    category_id INT(11) NOT NULL,
    department_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_subject_category (subject_id, category_id),
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES subject_categories(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";
$conn->query($mappingTableQuery);

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_category'])) {
        $category_name = trim($_POST['category_name']);
        $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : NULL;
        $description = trim($_POST['description']);
        
        if (!empty($category_name)) {
            $stmt = $conn->prepare("INSERT INTO subject_categories (category_name, department_id, description) VALUES (?, ?, ?)");
            $stmt->bind_param("sis", $category_name, $department_id, $description);
            
            if ($stmt->execute()) {
                $message = "বিষয় ক্যাটাগরি সফলভাবে যোগ করা হয়েছে!";
                $messageType = "success";
            } else {
                $message = "ক্যাটাগরি যোগ করতে সমস্যা হয়েছে!";
                $messageType = "danger";
            }
            $stmt->close();
        }
    }
    
    if (isset($_POST['assign_subjects'])) {
        $category_id = $_POST['category_id'];
        $subject_ids = $_POST['subject_ids'] ?? [];
        $department_id = !empty($_POST['assign_department_id']) ? $_POST['assign_department_id'] : NULL;
        
        if (!empty($category_id) && !empty($subject_ids)) {
            // First, remove existing mappings for this category
            $deleteStmt = $conn->prepare("DELETE FROM subject_category_mapping WHERE category_id = ?");
            $deleteStmt->bind_param("i", $category_id);
            $deleteStmt->execute();
            $deleteStmt->close();
            
            // Add new mappings
            $insertStmt = $conn->prepare("INSERT INTO subject_category_mapping (subject_id, category_id, department_id) VALUES (?, ?, ?)");
            $success_count = 0;
            
            foreach ($subject_ids as $subject_id) {
                $insertStmt->bind_param("iii", $subject_id, $category_id, $department_id);
                if ($insertStmt->execute()) {
                    $success_count++;
                }
            }
            $insertStmt->close();
            
            $message = "$success_count টি বিষয় ক্যাটাগরিতে যোগ করা হয়েছে!";
            $messageType = "success";
        }
    }
    
    if (isset($_POST['delete_category'])) {
        $category_id = $_POST['category_id'];
        
        $stmt = $conn->prepare("DELETE FROM subject_categories WHERE id = ?");
        $stmt->bind_param("i", $category_id);
        
        if ($stmt->execute()) {
            $message = "ক্যাটাগরি সফলভাবে মুছে ফেলা হয়েছে!";
            $messageType = "success";
        } else {
            $message = "ক্যাটাগরি মুছতে সমস্যা হয়েছে!";
            $messageType = "danger";
        }
        $stmt->close();
    }
}

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all subjects - check if department_id column exists first
$checkColumns = $conn->query("SHOW COLUMNS FROM subjects");
$hasDepId = false;
while ($col = $checkColumns->fetch_assoc()) {
    if ($col['Field'] == 'department_id') {
        $hasDepId = true;
        break;
    }
}

// Check if is_active column exists in subjects table
$checkSubActiveQuery = "SHOW COLUMNS FROM subjects LIKE 'is_active'";
$subActiveResult = $conn->query($checkSubActiveQuery);
$hasSubActive = ($subActiveResult->num_rows > 0);

if ($hasDepId) {
    if ($hasSubActive) {
        $subjectsQuery = "SELECT s.*, d.department_name FROM subjects s
                          LEFT JOIN departments d ON s.department_id = d.id
                          WHERE s.is_active = 1
                          ORDER BY s.subject_name";
    } else {
        $subjectsQuery = "SELECT s.*, d.department_name FROM subjects s
                          LEFT JOIN departments d ON s.department_id = d.id
                          ORDER BY s.subject_name";
    }
} else {
    if ($hasSubActive) {
        $subjectsQuery = "SELECT s.*, '' as department_name FROM subjects s
                          WHERE s.is_active = 1
                          ORDER BY s.subject_name";
    } else {
        $subjectsQuery = "SELECT s.*, '' as department_name FROM subjects s
                          ORDER BY s.subject_name";
    }
}
$subjects = $conn->query($subjectsQuery);

// Check if department_id column exists in subject_categories table
$checkCatDeptColumnQuery = "SHOW COLUMNS FROM subject_categories LIKE 'department_id'";
$catDeptColumnResult = $conn->query($checkCatDeptColumnQuery);
$hasCatDept = ($catDeptColumnResult->num_rows > 0);

// Check if is_active column exists in subject_categories table
$checkCatActiveColumnQuery = "SHOW COLUMNS FROM subject_categories LIKE 'is_active'";
$catActiveColumnResult = $conn->query($checkCatActiveColumnQuery);
$hasCatActive = ($catActiveColumnResult->num_rows > 0);

// Check if is_active column exists in subjects table
$checkSubActiveColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'is_active'";
$subActiveColumnResult = $conn->query($checkSubActiveColumnQuery);
$hasSubActive = ($subActiveColumnResult->num_rows > 0);

// Build categories query based on available columns
if ($hasCatDept) {
    $categoriesQuery = "SELECT sc.*, d.department_name,
                        GROUP_CONCAT(DISTINCT s.subject_name SEPARATOR ', ') as assigned_subjects,
                        COUNT(DISTINCT scm.subject_id) as subject_count
                        FROM subject_categories sc
                        LEFT JOIN departments d ON sc.department_id = d.id
                        LEFT JOIN subject_category_mapping scm ON sc.id = scm.category_id
                        LEFT JOIN subjects s ON scm.subject_id = s.id";
} else {
    $categoriesQuery = "SELECT sc.*, '' as department_name,
                        GROUP_CONCAT(DISTINCT s.subject_name SEPARATOR ', ') as assigned_subjects,
                        COUNT(DISTINCT scm.subject_id) as subject_count
                        FROM subject_categories sc
                        LEFT JOIN subject_category_mapping scm ON sc.id = scm.category_id
                        LEFT JOIN subjects s ON scm.subject_id = s.id";
}

// Add WHERE conditions based on available columns
$whereConditions = [];
if ($hasCatActive) {
    $whereConditions[] = "sc.is_active = 1";
}
if ($hasSubActive) {
    $whereConditions[] = "(s.is_active = 1 OR s.id IS NULL)";
}

// Add department filter if selected
if (isset($_GET['filter_dept']) && !empty($_GET['filter_dept']) && $hasCatDept) {
    $filterDept = intval($_GET['filter_dept']);
    $whereConditions[] = "sc.department_id = $filterDept";
}

if (!empty($whereConditions)) {
    $categoriesQuery .= " WHERE " . implode(" AND ", $whereConditions);
}

$categoriesQuery .= " GROUP BY sc.id, sc.category_name, sc.department_id, d.department_name
                     ORDER BY sc.category_name";

$categories = $conn->query($categoriesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ক্যাটাগরি ব্যবস্থাপনা - জামিয়া ফরিদিয়া আরাবিয়া ওয়াকফ</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Hind Siliguri', sans-serif !important;
        }
        
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        /* Sidebar Styles */
        .sidebar {
            background: #2c3e50 !important;
            color: white !important;
            min-height: 100vh;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
        }
        
        .sidebar h3 {
            color: white !important;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 12px 20px;
            margin: 5px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1) !important;
            color: white !important;
            transform: translateX(5px);
        }
        
        .sidebar .nav-link.active {
            background-color: #3498db !important;
            color: white !important;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
            background-color: #f8f9fa;
            min-height: 100vh;
            width: calc(100% - 250px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            border-radius: 10px;
            padding: 8px 16px;
            font-weight: 600;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            border: none;
            border-radius: 10px;
            padding: 8px 16px;
            font-weight: 600;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-weight: 500;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border: none;
            padding: 15px;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
            border: none;
            border-bottom: 1px solid #e9ecef;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
            border-radius: 20px;
        }
        
        .subject-list {
            max-width: 300px;
            word-wrap: break-word;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
                position: fixed;
                width: 100%;
                height: auto;
                z-index: 1050;
                overflow-y: auto;
                max-height: 300px;
            }
            
            .main-content {
                margin-left: 0;
                margin-top: 300px;
                width: 100%;
            }
            
            .sidebar .nav-link {
                border-radius: 10px;
                margin: 2px 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="text-center mb-4">
            <h3>এডমিন প্যানেল</h3>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="admin_dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="students.php">
                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="teachers.php">
                    <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="staff.php">
                    <i class="fas fa-user-tie me-2"></i> কর্মচারী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="departments.php">
                    <i class="fas fa-building me-2"></i> বিভাগ
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="subjects.php">
                    <i class="fas fa-book-open me-2"></i> বিষয়
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="subject_categories.php">
                    <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../includes/logout.inc.php">
                    <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col">
                    <h2>বিষয় ক্যাটাগরি ব্যবস্থাপনা</h2>
                    <p class="text-muted">বিভাগ অনুযায়ী বিষয়ের ক্যাটাগরি নির্ধারণ করুন - একক বা একাধিক বিষয় একসাথে</p>
                </div>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Add New Category Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>নতুন ক্যাটাগরি যোগ করুন</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="category_name" class="form-label">ক্যাটাগরি নাম *</label>
                                <input type="text" class="form-control" id="category_name" name="category_name" required>
                            </div>
                            <div class="col-md-4">
                                <label for="department_id" class="form-label">বিভাগ (ঐচ্ছিক)</label>
                                <select class="form-select" id="department_id" name="department_id" onchange="syncDepartmentSelection()">
                                    <option value="">সব বিভাগ</option>
                                    <?php
                                    $departments->data_seek(0);
                                    while($dept = $departments->fetch_assoc()): ?>
                                        <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['department_name']); ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="description" class="form-label">বিবরণ</label>
                                <input type="text" class="form-control" id="description" name="description" placeholder="ক্যাটাগরি সম্পর্কে বিবরণ">
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" name="add_category" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>ক্যাটাগরি যোগ করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Assign Subjects to Category Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-link me-2"></i>বিষয় ক্যাটাগরিতে যোগ করুন</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="category_id" class="form-label">ক্যাটাগরি নির্বাচন করুন *</label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">ক্যাটাগরি নির্বাচন করুন</option>
                                    <?php
                                    $categories->data_seek(0);
                                    while($cat = $categories->fetch_assoc()): ?>
                                        <option value="<?php echo $cat['id']; ?>">
                                            <?php echo htmlspecialchars($cat['category_name']); ?>
                                            <?php if($cat['department_name']): ?>
                                                (<?php echo htmlspecialchars($cat['department_name']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="assign_department_id" class="form-label">বিভাগ নির্বাচন</label>
                                <select class="form-select" id="assign_department_id" name="assign_department_id" onchange="filterSubjectsByDepartment()">
                                    <option value="">সব বিভাগ</option>
                                    <?php
                                    $departments->data_seek(0);
                                    while($dept = $departments->fetch_assoc()): ?>
                                        <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['department_name']); ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">বিষয় নির্বাচন করুন (একাধিক নির্বাচন করতে Ctrl চেপে রাখুন) *</label>
                                <select class="form-select" name="subject_ids[]" multiple size="8" required id="subjects_select">
                                    <?php
                                    // Get subjects with proper department mapping
                                    $subjectsWithDeptQuery = "SELECT DISTINCT s.id, s.subject_name,
                                                             COALESCE(s.department_id, sd.department_id) as dept_id,
                                                             d.department_name
                                                             FROM subjects s
                                                             LEFT JOIN subject_departments sd ON s.id = sd.subject_id
                                                             LEFT JOIN departments d ON COALESCE(s.department_id, sd.department_id) = d.id
                                                             WHERE s.is_active = 1 OR s.is_active IS NULL
                                                             ORDER BY s.subject_name";
                                    $subjectsWithDept = $conn->query($subjectsWithDeptQuery);

                                    if ($subjectsWithDept):
                                        while($subject = $subjectsWithDept->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $subject['id']; ?>" data-department="<?php echo $subject['dept_id'] ?? ''; ?>">
                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            <?php if($subject['department_name']): ?>
                                                - <?php echo htmlspecialchars($subject['department_name']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    endif;
                                    ?>
                                </select>
                                <small class="text-muted">একাধিক বিষয় নির্বাচন করতে Ctrl কী চেপে রেখে ক্লিক করুন</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" name="assign_subjects" class="btn btn-success">
                                <i class="fas fa-link me-2"></i>বিষয় যোগ করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Categories List Card -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>বিদ্যমান ক্যাটাগরি সমূহ</h5>
                        </div>
                        <div class="col-auto">
                            <form method="GET" class="d-flex">
                                <select name="filter_dept" class="form-select form-select-sm me-2" onchange="this.form.submit()">
                                    <option value="">সব বিভাগ</option>
                                    <?php
                                    $departments->data_seek(0);
                                    while($dept = $departments->fetch_assoc()):
                                        $selected = (isset($_GET['filter_dept']) && $_GET['filter_dept'] == $dept['id']) ? 'selected' : '';
                                    ?>
                                        <option value="<?php echo $dept['id']; ?>" <?php echo $selected; ?>>
                                            <?php echo htmlspecialchars($dept['department_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($categories->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ক্যাটাগরি নাম</th>
                                        <th>বিভাগ</th>
                                        <th>বিষয় সংখ্যা</th>
                                        <th>যুক্ত বিষয়সমূহ</th>
                                        <th>কার্যক্রম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $categories->data_seek(0);
                                    while($category = $categories->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($category['category_name']); ?></strong>
                                                <?php if($category['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($category['description']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($category['department_name']): ?>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($category['department_name']); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">সব বিভাগ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $category['subject_count']; ?> টি</span>
                                            </td>
                                            <td class="subject-list">
                                                <?php if($category['assigned_subjects']): ?>
                                                    <small><?php echo htmlspecialchars($category['assigned_subjects']); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">কোনো বিষয় যুক্ত নেই</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('আপনি কি নিশ্চিত যে এই ক্যাটাগরি মুছে ফেলতে চান?');">
                                                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                    <button type="submit" name="delete_category" class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash me-1"></i>মুছুন
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোনো ক্যাটাগরি পাওয়া যায়নি</h5>
                            <p class="text-muted">উপরের ফর্ম ব্যবহার করে নতুন ক্যাটাগরি যোগ করুন</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Department filter for subjects
        function filterSubjectsByDepartment() {
            const departmentId = document.getElementById('assign_department_id').value;
            const subjectSelect = document.getElementById('subjects_select');
            const options = subjectSelect.querySelectorAll('option');

            console.log('Filtering by department:', departmentId); // Debug

            let visibleCount = 0;
            options.forEach(option => {
                const optionDepartment = option.getAttribute('data-department');
                console.log('Option:', option.textContent, 'Department:', optionDepartment); // Debug

                if (departmentId === '' || departmentId === optionDepartment || optionDepartment === '') {
                    option.style.display = '';
                    visibleCount++;
                } else {
                    option.style.display = 'none';
                    // Clear selection if hidden
                    option.selected = false;
                }
            });

            console.log('Visible options:', visibleCount); // Debug

            // Auto-select the same department in the category assignment section
            const categoryDeptSelect = document.getElementById('department_id');
            if (categoryDeptSelect && departmentId !== '') {
                categoryDeptSelect.value = departmentId;
            }

            // Show message if no subjects found
            if (visibleCount === 0 && departmentId !== '') {
                // You could add a message here
                console.log('No subjects found for this department');
            }
        }

        // Sync department selection between forms
        function syncDepartmentSelection() {
            const categoryDeptSelect = document.getElementById('department_id');
            const assignDeptSelect = document.getElementById('assign_department_id');

            if (categoryDeptSelect && assignDeptSelect) {
                // Sync from category form to assignment form
                assignDeptSelect.value = categoryDeptSelect.value;
                // Trigger filter
                filterSubjectsByDepartment();
            }
        }

        // Initialize filter on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener
            const deptSelect = document.getElementById('assign_department_id');
            if (deptSelect) {
                deptSelect.addEventListener('change', filterSubjectsByDepartment);
            }

            // Initial filter
            filterSubjectsByDepartment();
        });
    </script>
</body>
</html>
